# مشخصات پروژه شارن

## 1. سیستم کاربری و احراز هویت
### سطوح دسترسی
- ادمین: دسترسی کامل به تمام بخش‌ها، مدیریت کاربران، تنظیمات سیستم
- مدیر: مدیریت فروشندگان، مشتریان، محصولات و گزارش‌ها
- فروشنده: ثبت فروش، مدیریت مشتریان خود، مشاهده گزارش‌های شخصی

### سیستم ثبت‌نام و ورود
- ثبت‌نام با ایمیل و رمز عبور
- تأیید ایمیل
- بازیابی رمز عبور
- ورود با کد تأیید
- حفظ وضعیت ورود
- خروج از حساب کاربری

### پروفایل کاربری
- ویرایش اطلاعات شخصی
- تغییر رمز عبور
- آپلود تصویر پروفایل
- مشاهده تاریخچه فعالیت‌ها
- تنظیمات اعلان‌ها

### تأیید کاربران
- تأیید توسط ادمین
- ارسال ایمیل تأیید
- محدودیت دسترسی تا تأیید
- امکان رد درخواست با دلیل

## 2. مدیریت مشتریان
### ثبت مشتری
- اطلاعات پایه (نام، شماره تماس، آدرس)
- اطلاعات تکمیلی (کد ملی، تاریخ تولد)
- ثبت معرف (کد معرف)
- تعیین اعتبار اولیه
- آپلود مدارک

### جستجو و فیلتر
- جستجو بر اساس نام، شماره تماس، کد ملی
- فیلتر بر اساس وضعیت پرداخت
- فیلتر بر اساس تاریخ ثبت‌نام
- فیلتر بر اساس معرف

### جزئیات مشتری
- اطلاعات شخصی
- تاریخچه خرید
- وضعیت مالی
- لیست فاکتورها
- وضعیت اقساط
- اطلاعات معرف

### مدیریت بدهی
- محاسبه بدهی کل
- تاریخچه پرداخت‌ها
- مدیریت اقساط
- گزارش بدهی
- یادآوری پرداخت

### سیستم معرف
- کد معرف اختصاصی
- محاسبه پاداش
- گزارش عملکرد معرف
- تسویه حساب با معرف

## 3. مدیریت محصولات
### افزودن محصول
- اطلاعات پایه (نام، قیمت، موجودی)
- دسته‌بندی
- تصاویر محصول
- مشخصات فنی
- کد محصول
- قیمت خرید و فروش
- حداقل موجودی

### ویرایش محصول
- بروزرسانی قیمت
- تغییر موجودی
- ویرایش مشخصات
- تغییر تصاویر
- تغییر دسته‌بندی

### مدیریت موجودی
- ثبت ورود و خروج
- هشدار موجودی کم
- گزارش موجودی
- تاریخچه تغییرات
- شمارش فیزیکی

### دسته‌بندی
- ساختار درختی
- زیردسته‌ها
- مرتب‌سازی
- فیلتر محصولات

## 4. سیستم فاکتور
### ایجاد فاکتور
- انتخاب مشتری
- افزودن محصولات
- محاسبه قیمت
- اعمال تخفیف
- ثبت توضیحات
- انتخاب روش پرداخت

### مدیریت پرداخت
- پرداخت نقدی
- پرداخت اقساطی
- پرداخت با کارت
- ثبت چک
- مدیریت پیش‌پرداخت

### چاپ و تأیید
- قالب‌های چاپ
- تأیید توسط مدیر
- تأیید پرداخت
- بایگانی فاکتور

## 5. مدیریت مالی
### گزارش‌های مالی
- گزارش روزانه
- گزارش ماهانه
- گزارش سالانه
- گزارش سود و زیان
- گزارش بدهی‌ها

### مدیریت پرداخت
- ثبت پرداخت‌ها
- تأیید پرداخت
- تاریخچه پرداخت
- گزارش پرداخت‌ها

### سیستم اقساط
- تعیین اقساط
- محاسبه سود
- یادآوری پرداخت
- گزارش اقساط

## 6. داشبورد و گزارش‌گیری
### داشبورد ادمین
- آمار کلی
- وضعیت فروش
- وضعیت کاربران
- گزارش‌های مالی
- مدیریت سیستم

### داشبورد مدیر
- آمار فروش
- وضعیت فروشندگان
- گزارش‌های مالی
- مدیریت محصولات

### داشبورد فروشنده
- آمار شخصی
- لیست مشتریان
- گزارش فروش
- وضعیت پرداخت‌ها

## 7. سیستم نوتیفیکیشن
### اعلان‌های سیستمی
- اعلان‌های سیستمی
- اعلان‌های مالی
- اعلان‌های تأیید
- مدیریت اعلان‌ها

## 8. ویژگی‌های امنیتی
### رمزنگاری
- رمزنگاری داده‌ها
- رمزنگاری ارتباطات
- مدیریت کلیدها

### مدیریت نشست
- کنترل دسترسی
- محدودیت زمان
- خروج خودکار

### لاگ و پشتیبان
- ثبت فعالیت‌ها
- پشتیبان‌گیری خودکار
- بازیابی اطلاعات

## 9. ویژگی‌های عمومی
### رابط کاربری
- طراحی فارسی
- پشتیبانی RTL
- تم روشن/تاریک
- رابط کاربری واکنش‌گرا

### جستجو و فیلتر
- جستجوی پیشرفته
- فیلتر چندگانه
- مرتب‌سازی
- ذخیره فیلترها

## 10. تکنولوژی‌ها
### فرانت‌اند
- Kotlin
- Android SDK
- Material Design
- Navigation Component
- ViewModel/LiveData
- DataBinding

### بک‌اند
- Supabase
- Room Database
- Coroutines
- Retrofit
- Glide

## 11. ساختار پروژه
### معماری
- MVVM
- Clean Architecture
- Repository Pattern
- Dependency Injection

### تست
- Unit Testing
- UI Testing
- Integration Testing

## 12. ویژگی‌های اضافی
### سخت‌افزار
- پشتیبانی از چاپگر
- اسکنر بارکد
- کارت‌خوان

### گزارش‌گیری
- خروجی PDF
- خروجی Excel
- نمودارها

### مدیریت
- سیستم پاداش
- مدیریت انبار
- ارسال پیامک
- چند ارزی 